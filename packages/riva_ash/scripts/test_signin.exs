#!/usr/bin/env elixir

# Test script to verify sign-in functionality
Mix.install([
  {:httpoison, "~> 2.0"}
])

defmodule SignInTester do
  def test_signin_page do
    IO.puts("🧪 Testing sign-in page...")
    
    case HTTPoison.get("http://localhost:4000/sign-in") do
      {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
        if String.contains?(body, "Sign in to your account") and 
           String.contains?(body, "phx-submit=\"sign_in\"") do
          IO.puts("✅ Sign-in page loads correctly with LiveView form")
          :ok
        else
          IO.puts("❌ Sign-in page missing expected content")
          :error
        end
      
      {:ok, %HTTPoison.Response{status_code: status}} ->
        IO.puts("❌ Sign-in page returned status #{status}")
        :error
        
      {:error, reason} ->
        IO.puts("❌ Failed to connect to sign-in page: #{inspect(reason)}")
        :error
    end
  end
  
  def test_protected_route_redirect do
    IO.puts("🧪 Testing protected route redirect...")
    
    case HTTPoison.get("http://localhost:4000/businesses", follow_redirect: false) do
      {:ok, %HTTPoison.Response{status_code: 302, headers: headers}} ->
        location = Enum.find_value(headers, fn
          {"location", loc} -> loc
          {"Location", loc} -> loc
          _ -> nil
        end)
        
        if location && String.contains?(location, "/sign-in") do
          IO.puts("✅ Protected route correctly redirects to sign-in")
          :ok
        else
          IO.puts("❌ Protected route redirect location: #{inspect(location)}")
          :error
        end
      
      {:ok, %HTTPoison.Response{status_code: status}} ->
        IO.puts("❌ Protected route returned status #{status} instead of redirect")
        :error
        
      {:error, reason} ->
        IO.puts("❌ Failed to test protected route: #{inspect(reason)}")
        :error
    end
  end
  
  def run_tests do
    IO.puts("🚀 Starting sign-in functionality tests...\n")
    
    results = [
      test_signin_page(),
      test_protected_route_redirect()
    ]
    
    success_count = Enum.count(results, &(&1 == :ok))
    total_count = length(results)
    
    IO.puts("\n📊 Test Results:")
    IO.puts("✅ Passed: #{success_count}/#{total_count}")
    
    if success_count == total_count do
      IO.puts("🎉 All sign-in tests passed!")
      System.halt(0)
    else
      IO.puts("❌ Some tests failed")
      System.halt(1)
    end
  end
end

SignInTester.run_tests()
